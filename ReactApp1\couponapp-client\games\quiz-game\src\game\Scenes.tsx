import React, { useRef } from 'react'

import { AssetUrl } from '@repo/shared/lib/types/widgetSettings'
import { useAtom } from 'jotai'
import { gamePreviewScreenAtomFamily, selectedEditorItem<PERSON>tom } from '@repo/shared/lib/atoms/editor-atoms'

import MainGame, { useGameState } from './GameMain'
import { GameContainer } from '@repo/shared-game-utils/components/GameContainer'
import { GameButton } from '@repo/shared-game-utils/components/GameButton'
import { GameText } from '@repo/shared-game-utils/components/GameText'
import { useGame } from '@repo/shared-game-utils/hooks/useGame'
import { defaultGameConfig, ReactGameConfig } from '../types/config'
import { useMusic } from '@repo/shared-game-utils/hooks/useSounds'
import { UiWidgetContainer } from '@repo/shared-game-utils/components/UiWidgetContainer'
import { useRollResult } from '@repo/shared/lib/rewards/hooks/useRoundReward'

export const StartScreen: React.FC<{ onButtonClick: () => void }> = ({ onButtonClick }) => {
    const { config, resolveAssetUrl } = useGame<ReactGameConfig>()

    return (
        <div className="h-full w-full flex flex-col items-center justify-center">
            <GameContainer
                config={config.startScreenOverlay}
                dataConfigKey="startScreenOverlay"
                resolveAssetUrl={resolveAssetUrl}
                onClick={(e) => {
                    e.stopPropagation()
                }}
            >
                <GameText
                    config={config.startScreenTitle}
                    dataConfigKey="startScreenTitle"
                    className="mb-6"
                    onClick={(e) => {
                        e.stopPropagation()
                    }}
                />
                <GameButton
                    config={config.startScreenStartButton}
                    dataConfigKey="startScreenStartButton"
                    onClick={(e) => {
                        e.stopPropagation()
                        if (onButtonClick) {
                            onButtonClick()
                        }
                    }}
                />
            </GameContainer>
        </div>
    )
}





// Game Over Win Screen - Shows when player won (reached target score or completed quiz)
export const GameOverWinScreen: React.FC<{ onButtonClick: () => void }> = ({ onButtonClick }) => {
    const { config } = useGame<ReactGameConfig>()
    useMusic(config.winSound, true, false)

        const { roundId } = useGameState()
    const rollResult = useRollResult(roundId)


    if (rollResult?.isLoading) {
        return <div className="h-full w-full flex flex-col items-center justify-center">
            Loading...
        </div>
    }
    
    return (
        <div className="h-full w-full flex flex-col items-center justify-center">
            <UiWidgetContainer widgetId={'gameOverWin'} />
        </div>
    )
}



// Game Over Lost Screen - Shows when player lost (out of lives or failed)
export const GameOverLostScreen: React.FC<{ onButtonClick: () => void }> = ({ onButtonClick }) => {
    const { config } = useGame<ReactGameConfig>()
    useMusic(config.gameOverSound, true, false)

    const { roundId } = useGameState()
    const rollResult = useRollResult(roundId)


    if (rollResult?.isLoading) {
        return <div className="h-full w-full flex flex-col items-center justify-center">
            Loading...
        </div>
    }

    return (
        <div className="h-full w-full flex flex-col items-center justify-center">
            <UiWidgetContainer widgetId={'gameOverLost'} />
        </div>
    )
}



export interface PreviewSceneProps {
    config: ReactGameConfig
    widgetId: string
    resolveAssetUrl: (id: AssetUrl) => string
    children?: any
}

export const PreviewScene: React.FC<PreviewSceneProps> = ({ config, widgetId, resolveAssetUrl, children }) => {
    const [selectedScreen, setSelectedScreen] = useAtom(gamePreviewScreenAtomFamily(widgetId))
    const [editorSelection, setEditorSelection] = useAtom(selectedEditorItemAtom)
    const initialGameScreenChecked = useRef(false)

    const currentScreen = selectedScreen ?? 'main'

    return (
        <MainGame config={config} widgetId={widgetId} isPreview={true} resolveAssetUrl={resolveAssetUrl} currentScreenId={currentScreen as any}
            initialGameScreenChecked={initialGameScreenChecked}
            children={children}
            setCurrentScreenId={() => { }} defaultConfig={defaultGameConfig} />
    )
}
